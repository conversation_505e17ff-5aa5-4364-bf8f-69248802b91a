#!/usr/bin/env python3
"""
Verify that Kernel Language Entropy uses correct default parameters from the paper
"""

from uq_methods.implementations.kernel_language_entropy import KernelLanguageEntropyUQ

def verify_defaults():
    """Verify default parameter values"""
    print("=== Verifying Kernel Language Entropy Default Parameters ===")
    
    # Create instance with default parameters
    kle = KernelLanguageEntropyUQ()
    
    print(f"Default temperature (t): {kle.t}")
    print(f"Default normalize: {kle.normalize}")
    print(f"Default scale: {kle.scale}")
    print(f"Default jitter: {kle.jitter}")
    print(f"Default model: {kle.model_name}")
    
    # Verify against paper recommendations
    expected_defaults = {
        't': 0.3,  # From paper
        'normalize': True,
        'scale': True,
        'jitter': 0,
        'model_name': 'microsoft/deberta-large-mnli'
    }
    
    print("\n=== Verification Results ===")
    all_correct = True
    
    for param, expected_value in expected_defaults.items():
        actual_value = getattr(kle, param)
        is_correct = actual_value == expected_value
        status = "✓" if is_correct else "✗"
        print(f"{status} {param}: {actual_value} (expected: {expected_value})")
        if not is_correct:
            all_correct = False
    
    if all_correct:
        print("\n🎉 All default parameters are correctly set according to the paper!")
    else:
        print("\n❌ Some default parameters need adjustment.")
    
    return all_correct

if __name__ == "__main__":
    verify_defaults()
