#!/usr/bin/env python3
"""
Test script for Kernel Language Entropy implementation
"""
import sys
import os
import numpy as np
import logging
from typing import List

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

from uq_methods.implementations.kernel_language_entropy import KernelLanguageEntropyUQ

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_basic_functionality():
    """Test basic functionality of Kernel Language Entropy"""
    print("\n=== Testing Basic Functionality ===")
    
    # Initialize the method
    kle = KernelLanguageEntropyUQ(verbose=True)
    
    # Test with simple responses
    responses = [
        "The weather is sunny today.",
        "It's a bright and sunny day.",
        "Today is rainy and cloudy.",
        "The sky is overcast with rain.",
        "Beautiful sunshine outside."
    ]
    
    print(f"Testing with {len(responses)} responses:")
    for i, resp in enumerate(responses):
        print(f"  {i+1}. {resp}")
    
    # Compute uncertainty
    result = kle.compute_uncertainty(responses)
    
    print(f"\nResults:")
    print(f"  Uncertainty Score: {result['uncertainty_score']:.4f}")
    print(f"  KLE Score: {result['kle_score']:.4f}")
    print(f"  Mean Entailment: {result['mean_entailment']:.4f}")
    print(f"  Mean Contradiction: {result['mean_contradiction']:.4f}")
    print(f"  Method: {result['method']}")
    print(f"  Number of Responses: {result['num_responses']}")
    
    return result


def test_edge_cases():
    """Test edge cases"""
    print("\n=== Testing Edge Cases ===")
    
    kle = KernelLanguageEntropyUQ(verbose=False)
    
    # Test with single response
    print("Testing with single response...")
    single_result = kle.compute_uncertainty(["Only one response"])
    print(f"  Single response uncertainty: {single_result['uncertainty_score']:.4f}")
    print(f"  Error message: {single_result.get('error', 'None')}")
    
    # Test with empty list
    print("\nTesting with empty list...")
    empty_result = kle.compute_uncertainty([])
    print(f"  Empty list uncertainty: {empty_result['uncertainty_score']:.4f}")
    print(f"  Error message: {empty_result.get('error', 'None')}")
    
    # Test with identical responses
    print("\nTesting with identical responses...")
    identical_responses = ["Same response"] * 5
    identical_result = kle.compute_uncertainty(identical_responses)
    print(f"  Identical responses uncertainty: {identical_result['uncertainty_score']:.4f}")
    print(f"  Mean entailment: {identical_result['mean_entailment']:.4f}")
    
    return single_result, empty_result, identical_result


def test_different_parameters():
    """Test with different parameter configurations"""
    print("\n=== Testing Different Parameters ===")
    
    responses = [
        "The cat is sleeping on the couch.",
        "A feline is resting on the sofa.",
        "The dog is running in the park.",
        "A canine is playing outside.",
        "Birds are flying in the sky."
    ]
    
    # Test different temperature values
    temperatures = [0.1, 0.3, 0.5, 1.0]
    print("Testing different temperature values:")
    for t in temperatures:
        kle = KernelLanguageEntropyUQ(t=t, verbose=False)
        result = kle.compute_uncertainty(responses)
        print(f"  t={t}: uncertainty={result['uncertainty_score']:.4f}")
    
    # Test with/without normalization
    print("\nTesting normalization options:")
    for normalize in [True, False]:
        kle = KernelLanguageEntropyUQ(normalize=normalize, verbose=False)
        result = kle.compute_uncertainty(responses)
        print(f"  normalize={normalize}: uncertainty={result['uncertainty_score']:.4f}")
    
    # Test with/without scaling
    print("\nTesting scaling options:")
    for scale in [True, False]:
        kle = KernelLanguageEntropyUQ(scale=scale, verbose=False)
        result = kle.compute_uncertainty(responses)
        print(f"  scale={scale}: uncertainty={result['uncertainty_score']:.4f}")


def test_semantic_similarity():
    """Test semantic similarity computation"""
    print("\n=== Testing Semantic Similarity ===")
    
    kle = KernelLanguageEntropyUQ(verbose=True)
    
    # Test with high semantic similarity
    high_similarity = [
        "The cat is sleeping.",
        "A cat is taking a nap.",
        "The feline is resting.",
        "A kitty is asleep.",
        "The cat is dozing."
    ]
    
    print("Testing high semantic similarity responses:")
    high_result = kle.compute_uncertainty(high_similarity)
    print(f"  High similarity uncertainty: {high_result['uncertainty_score']:.4f}")
    print(f"  Mean entailment: {high_result['mean_entailment']:.4f}")
    
    # Test with low semantic similarity
    low_similarity = [
        "The cat is sleeping.",
        "Mathematics is complex.",
        "The ocean is blue.",
        "Programming requires logic.",
        "Music brings joy."
    ]
    
    print("\nTesting low semantic similarity responses:")
    low_result = kle.compute_uncertainty(low_similarity)
    print(f"  Low similarity uncertainty: {low_result['uncertainty_score']:.4f}")
    print(f"  Mean entailment: {low_result['mean_entailment']:.4f}")
    
    return high_result, low_result


def test_method_interface():
    """Test the method interface compliance"""
    print("\n=== Testing Method Interface ===")
    
    kle = KernelLanguageEntropyUQ()
    
    # Test required samples
    required_samples = kle.get_required_samples()
    print(f"Required samples: {required_samples}")
    
    # Test method name
    method_name = kle.get_method_name()
    print(f"Method name: {method_name}")
    
    # Test that it's a proper BaseUQMethod
    from uq_methods.base import BaseUQMethod
    print(f"Is BaseUQMethod instance: {isinstance(kle, BaseUQMethod)}")
    
    return required_samples, method_name


def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("=" * 60)
    print("KERNEL LANGUAGE ENTROPY - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    try:
        # Test basic functionality
        basic_result = test_basic_functionality()
        
        # Test edge cases
        edge_results = test_edge_cases()
        
        # Test different parameters
        test_different_parameters()
        
        # Test semantic similarity
        semantic_results = test_semantic_similarity()
        
        # Test method interface
        interface_results = test_method_interface()
        
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print("✅ Basic functionality test: PASSED")
        print("✅ Edge cases test: PASSED")
        print("✅ Parameter variations test: PASSED")
        print("✅ Semantic similarity test: PASSED")
        print("✅ Method interface test: PASSED")
        print("\n🎉 All tests completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
