[{"group_key": {"task_type": "sentiment_analysis", "dataset_source": "twitter_sentiment", "prompt_seed": 1, "input_text": "We the best, no <PERSON><PERSON><PERSON>,We the best like <PERSON>, <PERSON>"}, "method": {"method_name": "KernelLanguageEntropy_default", "model_category": "kernel_entropy", "method_params": {"temperature": 0.3, "normalize": true, "scale": true, "jitter": 0}}, "outputs": {"uq_value": 0.9931365207905521, "metrics": {"kle_score": 0.9931365207905521, "mean_entailment": 0.9697241569176699, "mean_contradiction": 0.0010372795816063164, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:23.265335+00:00"}, "_id": "689b33a300bfd1c52fb74a90"}, {"group_key": {"task_type": "sentiment_analysis", "dataset_source": "twitter_sentiment", "prompt_seed": 1, "input_text": "We the best, no <PERSON><PERSON><PERSON>,We the best like <PERSON>, <PERSON>"}, "method": {"method_name": "KernelLanguageEntropy_t01", "model_category": "kernel_entropy", "method_params": {"temperature": 0.1, "normalize": true, "scale": true, "jitter": 0}}, "outputs": {"uq_value": 0.9938047413916857, "metrics": {"kle_score": 0.9938047413916857, "mean_entailment": 0.9697241569176699, "mean_contradiction": 0.0010372795816063164, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:23.380160+00:00"}, "_id": "689b33a300bfd1c52fb74a91"}, {"group_key": {"task_type": "sentiment_analysis", "dataset_source": "twitter_sentiment", "prompt_seed": 1, "input_text": "We the best, no <PERSON><PERSON><PERSON>,We the best like <PERSON>, <PERSON>"}, "method": {"method_name": "KernelLanguageEntropy_t05", "model_category": "kernel_entropy", "method_params": {"temperature": 0.5, "normalize": true, "scale": true, "jitter": 0}}, "outputs": {"uq_value": 0.9931343089525361, "metrics": {"kle_score": 0.9931343089525361, "mean_entailment": 0.9697241569176699, "mean_contradiction": 0.0010372795816063164, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:23.497486+00:00"}, "_id": "689b33a300bfd1c52fb74a92"}, {"group_key": {"task_type": "sentiment_analysis", "dataset_source": "twitter_sentiment", "prompt_seed": 1, "input_text": "We the best, no <PERSON><PERSON><PERSON>,We the best like <PERSON>, <PERSON>"}, "method": {"method_name": "KernelLanguageEntropy_no_norm", "model_category": "kernel_entropy", "method_params": {"temperature": 0.3, "normalize": false, "scale": false, "jitter": 0}}, "outputs": {"uq_value": -62436758.929026246, "metrics": {"kle_score": -62436758.929026246, "mean_entailment": 0.9697241569176699, "mean_contradiction": 0.0010372795816063164, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:23.615188+00:00"}, "_id": "689b33a300bfd1c52fb74a93"}, {"group_key": {"task_type": "explorative_coding", "dataset_source": "pytorch_commits", "prompt_seed": 24, "input_text": "Sparse CSR CPU: add `addmv_out` (#61536)\n\nSummary:\nPull Request resolved: https://github.com/pytorch/pytorch/pull/61536\n\nThis PR adds CPU dispatch for `addmv_out` with Sparse CSR matrix.\nThe implementation uses MKL Sparse library. If it's not available then a\nruntime error is thrown.\nSince structured_delegate is used we only need to implement the out variant, the in-place and normal variants are autogenerated.\n\nMKL descriptor of sparse matrices is implemented in `at::mkl::sparse::MklSparseCsrDescriptor`.\nMKL Sparse doesn't allow switching indices type in runtime, it's\npredetermined in build time. Only 32-bit version of MKL was tested\nlocally, but I expect 64-bit version to work correctly as well.\n\nWhen indices type of PyTorch CSR tensor doesn't match with MKL's,\nindices tensor is converted to MKL compatible type (`int` vs `int64_t`).\n\ncc nikitaved pearu cpuhrsch IvanYashchuk\n\nTest Plan: Imported from OSS\n\nReviewed By: ngimel\n\nDifferential Revision: *********\n\nPulled By: malfet\n\nfbshipit-source-id: b818a0b186aa227982221c3862a594266a58a2a6"}, "method": {"method_name": "KernelLanguageEntropy_default", "model_category": "kernel_entropy", "method_params": {"temperature": 0.3, "normalize": true, "scale": true, "jitter": 0}}, "outputs": {"uq_value": 0.9856054506468093, "metrics": {"kle_score": 0.9856054506468093, "mean_entailment": 0.4337665422389714, "mean_contradiction": 0.10143780707536885, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:47.960668+00:00"}, "_id": "689b33bb00bfd1c52fb74a94"}, {"group_key": {"task_type": "explorative_coding", "dataset_source": "pytorch_commits", "prompt_seed": 24, "input_text": "Sparse CSR CPU: add `addmv_out` (#61536)\n\nSummary:\nPull Request resolved: https://github.com/pytorch/pytorch/pull/61536\n\nThis PR adds CPU dispatch for `addmv_out` with Sparse CSR matrix.\nThe implementation uses MKL Sparse library. If it's not available then a\nruntime error is thrown.\nSince structured_delegate is used we only need to implement the out variant, the in-place and normal variants are autogenerated.\n\nMKL descriptor of sparse matrices is implemented in `at::mkl::sparse::MklSparseCsrDescriptor`.\nMKL Sparse doesn't allow switching indices type in runtime, it's\npredetermined in build time. Only 32-bit version of MKL was tested\nlocally, but I expect 64-bit version to work correctly as well.\n\nWhen indices type of PyTorch CSR tensor doesn't match with MKL's,\nindices tensor is converted to MKL compatible type (`int` vs `int64_t`).\n\ncc nikitaved pearu cpuhrsch IvanYashchuk\n\nTest Plan: Imported from OSS\n\nReviewed By: ngimel\n\nDifferential Revision: *********\n\nPulled By: malfet\n\nfbshipit-source-id: b818a0b186aa227982221c3862a594266a58a2a6"}, "method": {"method_name": "KernelLanguageEntropy_t01", "model_category": "kernel_entropy", "method_params": {"temperature": 0.1, "normalize": true, "scale": true, "jitter": 0}}, "outputs": {"uq_value": 0.9937509377994946, "metrics": {"kle_score": 0.9937509377994946, "mean_entailment": 0.4337665422389714, "mean_contradiction": 0.10143780707536885, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:48.078419+00:00"}, "_id": "689b33bc00bfd1c52fb74a95"}, {"group_key": {"task_type": "explorative_coding", "dataset_source": "pytorch_commits", "prompt_seed": 24, "input_text": "Sparse CSR CPU: add `addmv_out` (#61536)\n\nSummary:\nPull Request resolved: https://github.com/pytorch/pytorch/pull/61536\n\nThis PR adds CPU dispatch for `addmv_out` with Sparse CSR matrix.\nThe implementation uses MKL Sparse library. If it's not available then a\nruntime error is thrown.\nSince structured_delegate is used we only need to implement the out variant, the in-place and normal variants are autogenerated.\n\nMKL descriptor of sparse matrices is implemented in `at::mkl::sparse::MklSparseCsrDescriptor`.\nMKL Sparse doesn't allow switching indices type in runtime, it's\npredetermined in build time. Only 32-bit version of MKL was tested\nlocally, but I expect 64-bit version to work correctly as well.\n\nWhen indices type of PyTorch CSR tensor doesn't match with MKL's,\nindices tensor is converted to MKL compatible type (`int` vs `int64_t`).\n\ncc nikitaved pearu cpuhrsch IvanYashchuk\n\nTest Plan: Imported from OSS\n\nReviewed By: ngimel\n\nDifferential Revision: *********\n\nPulled By: malfet\n\nfbshipit-source-id: b818a0b186aa227982221c3862a594266a58a2a6"}, "method": {"method_name": "KernelLanguageEntropy_t05", "model_category": "kernel_entropy", "method_params": {"temperature": 0.5, "normalize": true, "scale": true, "jitter": 0}}, "outputs": {"uq_value": 0.9720193253845054, "metrics": {"kle_score": 0.9720193253845054, "mean_entailment": 0.4337665422389714, "mean_contradiction": 0.10143780707536885, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:48.196905+00:00"}, "_id": "689b33bc00bfd1c52fb74a96"}, {"group_key": {"task_type": "explorative_coding", "dataset_source": "pytorch_commits", "prompt_seed": 24, "input_text": "Sparse CSR CPU: add `addmv_out` (#61536)\n\nSummary:\nPull Request resolved: https://github.com/pytorch/pytorch/pull/61536\n\nThis PR adds CPU dispatch for `addmv_out` with Sparse CSR matrix.\nThe implementation uses MKL Sparse library. If it's not available then a\nruntime error is thrown.\nSince structured_delegate is used we only need to implement the out variant, the in-place and normal variants are autogenerated.\n\nMKL descriptor of sparse matrices is implemented in `at::mkl::sparse::MklSparseCsrDescriptor`.\nMKL Sparse doesn't allow switching indices type in runtime, it's\npredetermined in build time. Only 32-bit version of MKL was tested\nlocally, but I expect 64-bit version to work correctly as well.\n\nWhen indices type of PyTorch CSR tensor doesn't match with MKL's,\nindices tensor is converted to MKL compatible type (`int` vs `int64_t`).\n\ncc nikitaved pearu cpuhrsch IvanYashchuk\n\nTest Plan: Imported from OSS\n\nReviewed By: ngimel\n\nDifferential Revision: *********\n\nPulled By: malfet\n\nfbshipit-source-id: b818a0b186aa227982221c3862a594266a58a2a6"}, "method": {"method_name": "KernelLanguageEntropy_no_norm", "model_category": "kernel_entropy", "method_params": {"temperature": 0.3, "normalize": false, "scale": false, "jitter": 0}}, "outputs": {"uq_value": -1452271.652882909, "metrics": {"kle_score": -1452271.652882909, "mean_entailment": 0.4337665422389714, "mean_contradiction": 0.10143780707536885, "method": "KernelLanguageEntropy", "num_responses": 40}}, "meta": {"n_responses": 40, "test_type": "kernel_language_entropy_test"}, "timestamps": {"created_at": "2025-08-12 12:29:48.315729+00:00"}, "_id": "689b33bc00bfd1c52fb74a97"}]