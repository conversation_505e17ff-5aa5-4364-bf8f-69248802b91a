#!/usr/bin/env python3
"""
Test script for LofreeCP method on specific tasks.
Extracts 40 responses each from sentiment_analysis and explorative_coding tasks.
"""

import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from pymongo import MongoClient


# Import the LofreeCP UQ method
from uq_methods.implementations.LofreeCP import Lofree<PERSON>UQ


def get_sample_group(col, task_name: str, dataset_source: str, limit_responses: int = 40) -> Optional[Dict[str, Any]]:
    """
    Get a sample group with specified number of responses for a given task.
    Groups by prompt_seed - each seed should have exactly 40 responses for the same input.
    """
    # Find a prompt_seed with exactly the required number of responses
    pipeline = [
        {
            "$match": {
                "task_name": task_name,
                "dataset_source": dataset_source
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_seed": "$prompt_seed",
                },
                "count": {"$sum": 1},
                "docs": {"$push": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": {"$eq": limit_responses}  # Exactly 40 responses
            }
        },
        {
            "$limit": 1
        }
    ]

    result = list(col.aggregate(pipeline))
    if not result:
        print(f"No prompt_seed found for {task_name}/{dataset_source} with exactly {limit_responses} responses")
        return None

    group = result[0]
    docs = group["docs"]

    return {
        "group_id": group["_id"],
        "docs": docs,
        "count": len(docs)
    }


def build_group_key(doc: Dict[str, Any], group_id: Dict[str, Any]) -> Dict[str, Any]:
    """Build group key from a document and group_id."""
    return {
        "task_type": doc.get("task_name"),
        "dataset_source": doc.get("dataset_source"),
        "prompt_seed": group_id.get("prompt_seed"),
        "input_text": doc.get("input_text"),
    }


def extract_responses(docs: List[Dict[str, Any]], task_name: str) -> List[str]:
    """Extract responses from documents based on task type."""
    responses = []

    for doc in docs:
        if task_name == "sentiment_analysis":
            # Use parsed_answer for sentiment analysis
            response = doc.get("parsed_answer")
        elif task_name == "explorative_coding":
            # Use raw_answer for explorative coding
            response = doc.get("raw_answer")
        else:
            # Fallback: try parsed_answer first, then raw_answer
            response = doc.get("parsed_answer") or doc.get("raw_answer")

        if response:
            responses.append(str(response))

    return responses


def infer_reference_text(docs: List[Dict[str, Any]]) -> Optional[str]:
    """Infer reference text from documents."""
    try:
        refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
        if refs:
            from collections import Counter
            return Counter(refs).most_common(1)[0][0]
    except Exception:
        pass
    return None


def test_lofreecp_methods():
    """Main test function for LofreeCP methods."""
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client['LLM-UQ']
    col_src = db['response_collections']
    col_out = db['UQ_results_test']
    
    # Create index for output collection
    try:
        col_out.create_index([
            ("group_key.task_type", 1), ("group_key.dataset_source", 1), 
            ("group_key.prompt_variant", 1), ("group_key.prompt_seed", 1), 
            ("group_key.prompt_index", 1), ("method.method_name", 1)
        ], name="uq_test_group_method")
    except Exception:
        pass
    
    # Initialize LofreeCP methods with different parameter settings and embedding models
    methods = [
        ("LofreeCP_Frequency_Only", LofreeCPUQ(lambda1=0.0, lambda2=0.0, verbose=True)),
        ("LofreeCP_Freq_Entropy", LofreeCPUQ(lambda1=1.0, lambda2=0.0, verbose=True)),
        ("LofreeCP_E5_Full", LofreeCPUQ(lambda1=1.0, lambda2=1.0,
                                       embedding_model="intfloat/multilingual-e5-large-instruct", verbose=True)),
        ("LofreeCP_Qwen_Full", LofreeCPUQ(lambda1=1.0, lambda2=1.0,
                                         embedding_model="Qwen/Qwen3-Embedding-0.6B", verbose=True)),
        ("LofreeCP_E5_High", LofreeCPUQ(lambda1=2.0, lambda2=2.0,
                                       embedding_model="intfloat/multilingual-e5-large-instruct", verbose=True)),
        ("LofreeCP_Qwen_High", LofreeCPUQ(lambda1=2.0, lambda2=2.0,
                                         embedding_model="Qwen/Qwen3-Embedding-0.6B", verbose=True))
    ]
    
    # Test cases: (task_name, dataset_source)
    test_cases = [
        ("sentiment_analysis", "twitter_sentiment"),
        ("explorative_coding", "pytorch_commits")
    ]
    
    all_results = []
    
    print("Starting LofreeCP method testing...")
    
    for task_name, dataset_source in test_cases:
        print(f"\n=== Testing {task_name} / {dataset_source} ===")
        
        # Get sample group with 40 responses
        group_data = get_sample_group(col_src, task_name, dataset_source, limit_responses=40)
        if not group_data:
            continue
            
        docs = group_data["docs"]
        group_key = build_group_key(docs[0], group_data["group_id"])

        # Extract responses based on task type
        responses = extract_responses(docs, task_name)
        if len(responses) < 2:
            print(f"Not enough valid responses: {len(responses)}")
            continue

        print(f"Found {len(responses)} responses for testing")
        
        # Get reference text
        reference_text = infer_reference_text(docs)
        print(f"Reference text: {reference_text}")
        
        # Print sample input and responses
        print(f"Input text: {group_key['input_text'][:100]}...")
        print(f"Sample responses: {responses[:3]}")
        
        # Test each LofreeCP variant
        for method_name, method_instance in methods:
            print(f"\n--- Testing {method_name} ---")
            
            try:
                # Compute uncertainty
                result = method_instance.compute_uncertainty(responses)
                
                # Extract uncertainty score
                uq_value = result.get("uncertainty_score")
                
                print(f"UQ Score: {uq_value:.4f}")
                print(f"Normalized Entropy: {result.get('normalized_entropy', 'N/A'):.4f}")
                print(f"Unique Responses: {result.get('unique_responses', 'N/A')}")
                print(f"Most Frequent Response: {result.get('most_frequent_response', 'N/A')}")
                print(f"Response Diversity: {result.get('response_diversity', 'N/A'):.4f}")
                
                # Create record
                record = {
                    "group_key": group_key,
                    "method": {
                        "method_name": method_name,
                        "model_category": "LofreeCP",
                        "method_params": {
                            "lambda1": method_instance.lambda1,
                            "lambda2": method_instance.lambda2,
                            "embedding_model": method_instance.embedding_model
                        },
                    },
                    "outputs": {
                        "uq_value": uq_value,
                        "metrics": {k: v for k, v in result.items() 
                                  if k not in ("uncertainty_score",)}
                    },
                    "meta": {
                        "n_responses": len(responses),
                        "test_type": "lofreecp_methods_test"
                    },
                    "timestamps": {"created_at": datetime.now(timezone.utc)}
                }
                
                # Save to MongoDB
                col_out.insert_one(record)
                all_results.append(record)
                
                print(f"✓ {method_name} completed successfully")
                
            except Exception as e:
                print(f"✗ {method_name} failed: {str(e)}")
                import traceback
                traceback.print_exc()
                
                # Create error record
                error_record = {
                    "group_key": group_key,
                    "method": {"method_name": method_name},
                    "outputs": {"uq_value": None, "metrics": {"error": str(e)}},
                    "meta": {"n_responses": len(responses), "test_type": "lofreecp_methods_test"},
                    "timestamps": {"created_at": datetime.now(timezone.utc)}
                }
                col_out.insert_one(error_record)
                all_results.append(error_record)
    
    # Save results to JSON file
    output_file = "lofreecp_methods_test_results.json"
    with open(output_file, "w") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n=== Test Complete ===")
    print(f"Total results: {len(all_results)}")
    print(f"Results saved to MongoDB (UQ_results_test) and {output_file}")
    
    # Print summary
    print("\n=== Summary ===")
    for result in all_results:
        method_name = result["method"]["method_name"]
        task_type = result["group_key"]["task_type"]
        dataset_source = result["group_key"]["dataset_source"]
        uq_value = result["outputs"]["uq_value"]
        error = result["outputs"]["metrics"].get("error")
        
        if error:
            print(f"{method_name} on {task_type}/{dataset_source}: ERROR - {error}")
        else:
            print(f"{method_name} on {task_type}/{dataset_source}: UQ = {uq_value:.4f}")


if __name__ == "__main__":
    test_lofreecp_methods()
