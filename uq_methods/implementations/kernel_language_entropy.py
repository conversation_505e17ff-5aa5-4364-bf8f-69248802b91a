"""
Kernel Language Entropy method for uncertainty quantification
"""
import numpy as np
import scipy.linalg
import logging
from typing import List, Dict, Any
from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator

log = logging.getLogger(__name__)


def laplacian_matrix(weighted_graph: np.ndarray) -> np.ndarray:
    """Compute Laplacian matrix from weighted graph."""
    degrees = np.diag(np.sum(weighted_graph, axis=0))
    return weighted_graph - degrees


def heat_kernel(laplacian: np.ndarray, t: float) -> np.ndarray:
    """Compute heat kernel from Laplacian matrix."""
    return scipy.linalg.expm(-t * laplacian)


def normalize_kernel(K: np.ndarray) -> np.ndarray:
    """Normalize kernel matrix."""
    EPS = 1e-12
    diagonal_values = np.sqrt(np.diag(K)) + EPS
    normalized_kernel = K / np.outer(diagonal_values, diagonal_values)
    return normalized_kernel


def scale_entropy(entropy: np.ndarray, n_classes: int) -> np.ndarray:
    """Scale entropy by maximum possible entropy."""
    max_entropy = -np.log(1.0 / n_classes)  # For a discrete distribution with num_classes
    scaled_entropy = entropy / max_entropy
    return scaled_entropy


def vn_entropy(K: np.ndarray, normalize: bool, scale: bool, jitter: float) -> np.float64:
    """Compute von Neumann entropy of kernel matrix."""
    if normalize:
        K = normalize_kernel(K) / K.shape[0]
    result = 0
    eigvs = np.linalg.eig(K + jitter * np.eye(K.shape[0])).eigenvalues.astype(np.float64)
    for e in eigvs:
        if np.abs(e) > 1e-8:
            result -= e * np.log(e)
    if scale:
        result = scale_entropy(result, K.shape[0])
    return np.float64(result)


class KernelLanguageEntropyUQ(BaseUQMethod):
    """
    Kernel Language Entropy method for uncertainty quantification.

    Estimates the sequence-level uncertainty of a language model following the method of
    "Kernel Language Entropy" as provided in the paper https://arxiv.org/pdf/2405.20003

    This method calculates KLE(Kheat) = VNE(Kheat), where VNE is von Neumann entropy and
    Kheat is a heat kernel of a semantic graph over language model's outputs.
    """

    def __init__(self,
                 model_name: str = "microsoft/deberta-large-mnli",
                 t: float = 0.3,
                 normalize: bool = True,
                 scale: bool = True,
                 jitter: float = 0,
                 verbose: bool = False):
        """
        Initialize Kernel Language Entropy calculator.

        Parameters:
            model_name (str): NLI model to use for semantic similarity computation
            t (float): temperature for heat kernel; default is taken from the paper
            normalize (bool): whether VNE should be calculated on normalized kernel or not
            scale (bool): whether VNE should scale the result by amount of samples
            jitter (float): calculate VNE not on kernel, but kernel + jitter * I
            verbose (bool): Whether to print debug information
        """
        self.model_name = model_name
        self.t = t
        self.normalize = normalize
        self.scale = scale
        self.jitter = jitter
        self.verbose = verbose

        # Use shared cached NLI calculator
        self.nli_calc = get_nli_calculator(model_name)

    def _compute_nli_scores(self, text1: str, text2: str) -> tuple:
        """Compute all three NLI scores between two texts (cached)."""
        try:
            res = self.nli_calc.compute_nli_scores_cached(text1, text2)
            return float(res.entailment), float(res.neutral), float(res.contradiction)
        except Exception as e:
            log.warning(f"Error computing NLI score: {str(e)}")
            return 0.33, 0.34, 0.33  # Default to uniform distribution

    def _compute_semantic_matrices(self, responses: List[str]) -> tuple:
        """
        Compute semantic matrices for entailment and contradiction.

        Returns:
            Tuple of (entailment_matrix, contradiction_matrix)
        """
        n = len(responses)
        entailment_matrix = np.zeros((n, n))
        contradiction_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    entailment_matrix[i, j] = 1.0
                    contradiction_matrix[i, j] = 0.0
                else:
                    entailment, _, contradiction = self._compute_nli_scores(responses[i], responses[j])
                    entailment_matrix[i, j] = entailment
                    contradiction_matrix[i, j] = contradiction

        return entailment_matrix, contradiction_matrix

    def _compute_kernel_language_entropy(self, responses: List[str]) -> float:
        """
        Compute Kernel Language Entropy uncertainty score.

        Following the algorithm:
        1. Let S1, ..., Sn be a set of LLM generations.
        2. Let NLI'(Si, Sj) = one-hot prediction over (entailment, neutral class, contradiction)
        3. Let W be a matrix, such that Wij = wNLI'(Si, Sj), where w = (1, 0.5, 0)
        4. Let L be a laplacian matrix of W, i.e. L = W - D, where Dii = sum(Wij) over j.
        5. Let Kheat = heat kernel of W, i.e. Kheat = expm(-t * L), where t is a hyperparameter.
        6. Finally, KLE(x) = VNE(Kheat), where VNE(A) = -Tr(A log A).
        """
        if len(responses) < 2:
            return 0.0

        # Step 1-2: Compute NLI matrices
        entailment_matrix, contradiction_matrix = self._compute_semantic_matrices(responses)

        # Step 3: Compute weighted graph W = entailment + 0.5 * neutral
        # neutral = 1 - entailment - contradiction
        neutral_matrix = (
            np.ones(entailment_matrix.shape)
            - entailment_matrix
            - contradiction_matrix
        )
        weighted_graph = entailment_matrix + 0.5 * neutral_matrix

        # Step 4: Compute Laplacian matrix
        laplacian = laplacian_matrix(weighted_graph)

        # Step 5: Compute heat kernel
        heat_kernels = heat_kernel(laplacian, self.t)

        # Step 6: Compute von Neumann entropy
        kle_score = vn_entropy(heat_kernels, self.normalize, self.scale, self.jitter)

        return float(kle_score)

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """Compute uncertainty using Kernel Language Entropy."""
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "error": "Need at least 2 responses",
                "method": "KernelLanguageEntropy",
                "kle_score": 0.0
            }

        try:
            # Compute KLE uncertainty score
            kle_score = self._compute_kernel_language_entropy(responses)

            # Compute additional metrics for analysis
            entailment_matrix, contradiction_matrix = self._compute_semantic_matrices(responses)
            mean_entailment = np.mean(entailment_matrix[np.triu_indices_from(entailment_matrix, k=1)])
            mean_contradiction = np.mean(contradiction_matrix[np.triu_indices_from(contradiction_matrix, k=1)])

            if self.verbose:
                log.debug(f"Generated responses: {responses}")
                log.debug(f"KLE uncertainty score: {kle_score}")
                log.debug(f"Mean entailment: {mean_entailment}")
                log.debug(f"Mean contradiction: {mean_contradiction}")

            return {
                "uncertainty_score": kle_score,
                "kle_score": kle_score,
                "mean_entailment": mean_entailment,
                "mean_contradiction": mean_contradiction,
                "num_responses": len(responses),
                "method": "KernelLanguageEntropy",
                "entailment_matrix": entailment_matrix.tolist(),
                "contradiction_matrix": contradiction_matrix.tolist(),
                "metadata": {
                    "method": "KernelLanguageEntropy",
                    "model_name": self.model_name,
                    "temperature": self.t,
                    "normalize": self.normalize,
                    "scale": self.scale,
                    "jitter": self.jitter,
                    "verbose": self.verbose
                }
            }

        except Exception as e:
            log.error(f"Error computing Kernel Language Entropy uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "KernelLanguageEntropy",
                "kle_score": 1.0
            }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 5

    def get_method_name(self) -> str:
        """Get method name."""
        return "KernelLanguageEntropy"